{"cSpell.words": ["agentic", "A<PERSON>os", "biomimicry", "BMAD", "Brainwriting", "Centricity", "cicd", "dataclass", "docstrings", "emergently", "explorative", "fintech", "firmographic", "firmographics", "frontends", "gamedev", "golint", "Goroutines", "hotspots", "HSTS", "httpx", "Immer", "implementability", "Inclusivity", "kayvan", "<PERSON><PERSON>", "MERN", "mgmt", "nodir", "<PERSON><PERSON><PERSON>", "overcommitting", "pasteable", "pentest", "PESTEL", "Pi<PERSON>", "Polyre<PERSON>", "psychographics", "Pydantic", "pyproject", "reqs", "rescope", "roadmaps", "roleplay", "roomodes", "runbooks", "Serilog", "shadcn", "structlog", "subfolders", "Supabase", "Systemization", "taskroot", "Testcontainers", "tmpl", "tmplv", "touchpoints", "trpc", "Turborepo", "Underserved", "unredacted", "upgrader", "upgraders", "VARCHAR", "venv", "vercel", "Vite", "WCAG", "wireframes"], "markdownlint.config": {"MD033": {"allowed_elements": ["br", "div", "img", "rule", "sub"]}}}